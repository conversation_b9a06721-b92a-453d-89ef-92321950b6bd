{"name": "@mcpfun/mcp-server-memo", "version": "1.0.0", "description": "A lightweight MCP server for session memory management with persistent storage", "main": "dist/index.js", "type": "module", "files": ["dist/", "LICENSE", "README.md"], "scripts": {"build": "tsc", "start": "node dist/index.js", "start:log": "./start.sh", "dev": "ts-node --esm src/index.ts", "prepare": "npm run build", "prepublishOnly": "npm test", "test": "echo \"Error: no test specified\" && exit 0", "version": "git add -A", "postversion": "git push && git push --tags"}, "keywords": ["mcp", "model-context-protocol", "llm", "memory", "memo", "claude", "summary"], "author": "MCP Developer", "repository": {"type": "git", "url": "https://github.com/doggybee/mcp-server-memo.git"}, "bugs": {"url": "https://github.com/doggybee/mcp-server-memo/issues"}, "homepage": "https://github.com/doggybee/mcp-server-memo#readme", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "1.9.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "publishConfig": {"access": "public"}}