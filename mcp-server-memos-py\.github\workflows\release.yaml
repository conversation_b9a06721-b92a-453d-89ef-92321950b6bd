name: Create a Github Release

on:
  push:
    tags:
      - "*"

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # - name: Set up Python
      #   uses: actions/setup-python@v5
      #   with:
      #     python-version: 3.12
      #     cache: pip

      # - name: Install dependencies
      #   run: |
      #     python -m pip install --upgrade pip
      #     pip install uv
      #     uv sync

      # - name: Run tests
      #   run: make test

      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: |
            LICENSE
            README.md
          tag_name: ${{ github.ref_name }}
          release_name: Release ${{ github.ref_name }}
          draft: false
          prerelease: false
    
  publish_pypi:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install uv
          uv sync
      - name: Update version
        run: |
          sed -i "s/^version = \"0.1.15\"$/version = \"$GITHUB_REF_NAME\"/" pyproject.toml
      - name: Build
        run: uv build
      - name: Publish to test PyPI
        run: uv publish --publish-url https://test.pypi.org/legacy/
        env:
          UV_PUBLISH_USERNAME: __token__
          UV_PUBLISH_PASSWORD: ${{ secrets.TEST_PYPI_API_TOKEN }}
      - name: Publish to PyPI
        run: uv publish
        env:
          UV_PUBLISH_USERNAME: __token__
          UV_PUBLISH_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
