# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y gcc && rm -rf /var/lib/apt/lists/*

# Copy the project files
COPY . /app

# Upgrade pip
RUN pip install --upgrade pip

# Install the package using pip in editable mode
RUN pip install .

# Expose the port that the MCP server listens on - default from config, here using 8080
EXPOSE 8080

# Run the MCP server. The MCP server entrypoint is provided via the setuptools entrypoint 'mcp-server-memos'.
CMD ["mcp-server-memos", "--host", "localhost", "--port", "8080", "--token", ""]
