syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gen/api/v1";

service ResourceService {
  // CreateResource creates a new resource.
  rpc CreateResource(CreateResourceRequest) returns (Resource) {
    option (google.api.http) = {
      post: "/api/v1/resources",
      body: "resource"
    };
  }
  // ListResources lists all resources.
  rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse) {
    option (google.api.http) = {get: "/api/v1/resources"};
  }
  // SearchResources searches memos.
  rpc SearchResources(SearchResourcesRequest) returns (SearchResourcesResponse) {
    option (google.api.http) = {get: "/api/v1/resources:search"};
  }
  // GetResource returns a resource by name.
  rpc GetResource(GetResourceRequest) returns (Resource) {
    option (google.api.http) = {get: "/api/v1/{name=resources/*}"};
    option (google.api.method_signature) = "name";
  }
  // GetResourceBinary returns a resource binary by name.
  rpc GetResourceBinary(GetResourceBinaryRequest) returns (google.api.HttpBody) {
    option (google.api.http) = {get: "/file/{name=resources/*}/{filename}"};
    option (google.api.method_signature) = "name,filename";
  }
  // UpdateResource updates a resource.
  rpc UpdateResource(UpdateResourceRequest) returns (Resource) {
    option (google.api.http) = {
      patch: "/api/v1/{resource.name=resources/*}",
      body: "resource"
    };
    option (google.api.method_signature) = "resource,update_mask";
  }
  // DeleteResource deletes a resource by name.
  rpc DeleteResource(DeleteResourceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/api/v1/{name=resources/*}"};
    option (google.api.method_signature) = "name";
  }
}

message Resource {
  // The name of the resource.
  // Format: resources/{id}
  // id is the system generated unique identifier.
  string name = 1;

  // The user defined id of the resource.
  string uid = 2;

  google.protobuf.Timestamp create_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  string filename = 4;

  bytes content = 5 [(google.api.field_behavior) = INPUT_ONLY];

  string external_link = 6;

  string type = 7;

  int64 size = 8;

  // The related memo.
  // Format: memos/{id}
  optional string memo = 9;
}

message CreateResourceRequest {
  Resource resource = 1;
}

message ListResourcesRequest {}

message ListResourcesResponse {
  repeated Resource resources = 1;
}

message SearchResourcesRequest {
  string filter = 1;
}

message SearchResourcesResponse {
  repeated Resource resources = 1;
}

message GetResourceRequest {
  // The name of the resource.
  // Format: resources/{id}
  // id is the system generated unique identifier.
  string name = 1;
}

message GetResourceBinaryRequest {
  // The name of the resource.
  // Format: resources/{id}
  // id is the system generated unique identifier.
  string name = 1;

  // The filename of the resource. Mainly used for downloading.
  string filename = 2;
}

message UpdateResourceRequest {
  Resource resource = 1;

  google.protobuf.FieldMask update_mask = 2;
}

message DeleteResourceRequest {
  // The name of the resource.
  // Format: resources/{id}
  // id is the system generated unique identifier.
  string name = 1;
}
