[project]
name = "mcp-server-memos"
version = "0.1.15"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
license = { file = "LICENSE" }
dependencies = [
    "mcp>=1.1.0",
    "betterproto[compiler]>=2.0.0b6",
    "grpclib>=0.4.7",
    "pydantic>=2.10.3",
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
mcp-server-memos = "mcp_server_memos:main"

[project.urls]
Repository = "https://github.com/RyoJerryYu/mcp-server-memos-py"

[tool.uv]
package = true
