# Generated by the protocol buffer compiler.  DO NOT EDIT!
# sources: api/v1/activity_service.proto, api/v1/auth_service.proto, api/v1/common.proto, api/v1/idp_service.proto, api/v1/inbox_service.proto, api/v1/markdown_service.proto, api/v1/memo_relation_service.proto, api/v1/memo_service.proto, api/v1/reaction_service.proto, api/v1/resource_service.proto, api/v1/user_service.proto, api/v1/webhook_service.proto, api/v1/workspace_service.proto, api/v1/workspace_setting_service.proto
# plugin: python-betterproto
# This file has been @generated
import warnings
from dataclasses import dataclass
from datetime import datetime
from typing import (
    TYPE_CHECKING,
    Dict,
    List,
    Optional,
)

import betterproto
import betterproto.lib.google.protobuf as betterproto_lib_google_protobuf
import grpclib
from betterproto.grpc.grpclib_server import ServiceBase

from ....google import api as ___google_api__


if TYPE_CHECKING:
    import grpclib.server
    from betterproto.grpc.grpclib_client import MetadataLike
    from grpclib.metadata import Deadline


class RowStatus(betterproto.Enum):
    UNSPECIFIED = 0
    ACTIVE = 1
    ARCHIVED = 2


class UserRole(betterproto.Enum):
    ROLE_UNSPECIFIED = 0
    HOST = 1
    ADMIN = 2
    USER = 3


class IdentityProviderType(betterproto.Enum):
    TYPE_UNSPECIFIED = 0
    OAUTH2 = 1


class InboxStatus(betterproto.Enum):
    STATUS_UNSPECIFIED = 0
    UNREAD = 1
    ARCHIVED = 2


class InboxType(betterproto.Enum):
    TYPE_UNSPECIFIED = 0
    MEMO_COMMENT = 1
    VERSION_UPDATE = 2


class NodeType(betterproto.Enum):
    NODE_UNSPECIFIED = 0
    LINE_BREAK = 1
    PARAGRAPH = 2
    CODE_BLOCK = 3
    HEADING = 4
    HORIZONTAL_RULE = 5
    BLOCKQUOTE = 6
    ORDERED_LIST = 7
    UNORDERED_LIST = 8
    TASK_LIST = 9
    MATH_BLOCK = 10
    TABLE = 11
    EMBEDDED_CONTENT = 12
    TEXT = 13
    BOLD = 14
    ITALIC = 15
    BOLD_ITALIC = 16
    CODE = 17
    IMAGE = 18
    LINK = 19
    AUTO_LINK = 20
    TAG = 21
    STRIKETHROUGH = 22
    ESCAPING_CHARACTER = 23
    MATH = 24
    HIGHLIGHT = 25
    SUBSCRIPT = 26
    SUPERSCRIPT = 27
    REFERENCED_CONTENT = 28
    SPOILER = 29


class MemoRelationType(betterproto.Enum):
    TYPE_UNSPECIFIED = 0
    REFERENCE = 1
    COMMENT = 2


class ReactionType(betterproto.Enum):
    TYPE_UNSPECIFIED = 0
    THUMBS_UP = 1
    THUMBS_DOWN = 2
    HEART = 3
    FIRE = 4
    CLAPPING_HANDS = 5
    LAUGH = 6
    OK_HAND = 7
    ROCKET = 8
    EYES = 9
    THINKING_FACE = 10
    CLOWN_FACE = 11
    QUESTION_MARK = 12


class Visibility(betterproto.Enum):
    UNSPECIFIED = 0
    PRIVATE = 1
    PROTECTED = 2
    PUBLIC = 3


class WorkspaceStorageSettingStorageType(betterproto.Enum):
    STORAGE_TYPE_UNSPECIFIED = 0
    DATABASE = 1
    """DATABASE is the database storage type."""

    LOCAL = 2
    """LOCAL is the local storage type."""

    S3 = 3
    """S3 is the S3 storage type."""


@dataclass(eq=False, repr=False)
class Activity(betterproto.Message):
    id: int = betterproto.int32_field(1)
    """The system-generated unique identifier for the activity."""

    creator_id: int = betterproto.int32_field(2)
    """
    The system-generated unique identifier for the user who created the activity.
    """

    type: str = betterproto.string_field(3)
    """The type of the activity."""

    level: str = betterproto.string_field(4)
    """The level of the activity."""

    create_time: datetime = betterproto.message_field(5)
    """The create time of the activity."""

    payload: "ActivityPayload" = betterproto.message_field(6)
    """The payload of the activity."""


@dataclass(eq=False, repr=False)
class ActivityMemoCommentPayload(betterproto.Message):
    """
    ActivityMemoCommentPayload represents the payload of a memo comment activity.
    """

    memo_id: int = betterproto.int32_field(1)
    """The memo id of comment."""

    related_memo_id: int = betterproto.int32_field(2)
    """The memo id of related memo."""


@dataclass(eq=False, repr=False)
class ActivityVersionUpdatePayload(betterproto.Message):
    version: str = betterproto.string_field(1)
    """The updated version of memos."""


@dataclass(eq=False, repr=False)
class ActivityPayload(betterproto.Message):
    memo_comment: "ActivityMemoCommentPayload" = betterproto.message_field(1)
    version_update: "ActivityVersionUpdatePayload" = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class GetActivityRequest(betterproto.Message):
    id: int = betterproto.int32_field(1)
    """The system-generated unique identifier for the activity."""


@dataclass(eq=False, repr=False)
class PageToken(betterproto.Message):
    """Used internally for obfuscating the page token."""

    limit: int = betterproto.int32_field(1)
    offset: int = betterproto.int32_field(2)


@dataclass(eq=False, repr=False)
class User(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """

    id: int = betterproto.int32_field(2)
    """The system generated uid of the user."""

    role: "UserRole" = betterproto.enum_field(3)
    username: str = betterproto.string_field(4)
    email: str = betterproto.string_field(5)
    nickname: str = betterproto.string_field(6)
    avatar_url: str = betterproto.string_field(7)
    description: str = betterproto.string_field(8)
    password: str = betterproto.string_field(9)
    row_status: "RowStatus" = betterproto.enum_field(10)
    create_time: datetime = betterproto.message_field(11)
    update_time: datetime = betterproto.message_field(12)


@dataclass(eq=False, repr=False)
class ListUsersRequest(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class ListUsersResponse(betterproto.Message):
    users: List["User"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class SearchUsersRequest(betterproto.Message):
    filter: str = betterproto.string_field(1)
    """
    Filter is used to filter users returned in the list.
     Format: "username == 'frank'"
    """


@dataclass(eq=False, repr=False)
class SearchUsersResponse(betterproto.Message):
    users: List["User"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class GetUserRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """


@dataclass(eq=False, repr=False)
class GetUserAvatarBinaryRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """

    http_body: "___google_api__.HttpBody" = betterproto.message_field(2)
    """The raw HTTP body is bound to this field."""


@dataclass(eq=False, repr=False)
class CreateUserRequest(betterproto.Message):
    user: "User" = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class UpdateUserRequest(betterproto.Message):
    user: "User" = betterproto.message_field(1)
    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )


@dataclass(eq=False, repr=False)
class DeleteUserRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """


@dataclass(eq=False, repr=False)
class UserSetting(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """

    locale: str = betterproto.string_field(2)
    """The preferred locale of the user."""

    appearance: str = betterproto.string_field(3)
    """The preferred appearance of the user."""

    memo_visibility: str = betterproto.string_field(4)
    """The default visibility of the memo."""


@dataclass(eq=False, repr=False)
class GetUserSettingRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """


@dataclass(eq=False, repr=False)
class UpdateUserSettingRequest(betterproto.Message):
    setting: "UserSetting" = betterproto.message_field(1)
    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )


@dataclass(eq=False, repr=False)
class UserAccessToken(betterproto.Message):
    access_token: str = betterproto.string_field(1)
    description: str = betterproto.string_field(2)
    issued_at: datetime = betterproto.message_field(3)
    expires_at: datetime = betterproto.message_field(4)


@dataclass(eq=False, repr=False)
class ListUserAccessTokensRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """


@dataclass(eq=False, repr=False)
class ListUserAccessTokensResponse(betterproto.Message):
    access_tokens: List["UserAccessToken"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class CreateUserAccessTokenRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """

    description: str = betterproto.string_field(2)
    expires_at: Optional[datetime] = betterproto.message_field(3, optional=True)


@dataclass(eq=False, repr=False)
class DeleteUserAccessTokenRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the user.
     Format: users/{id}
    """

    access_token: str = betterproto.string_field(2)
    """access_token is the access token to delete."""


@dataclass(eq=False, repr=False)
class GetAuthStatusRequest(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class GetAuthStatusResponse(betterproto.Message):
    user: "User" = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class SignInRequest(betterproto.Message):
    username: str = betterproto.string_field(1)
    """The username to sign in with."""

    password: str = betterproto.string_field(2)
    """The password to sign in with."""

    never_expire: bool = betterproto.bool_field(3)
    """Whether the session should never expire."""


@dataclass(eq=False, repr=False)
class SignInWithSsoRequest(betterproto.Message):
    idp_id: int = betterproto.int32_field(1)
    """The ID of the SSO provider."""

    code: str = betterproto.string_field(2)
    """The code to sign in with."""

    redirect_uri: str = betterproto.string_field(3)
    """The redirect URI."""


@dataclass(eq=False, repr=False)
class SignUpRequest(betterproto.Message):
    username: str = betterproto.string_field(1)
    """The username to sign up with."""

    password: str = betterproto.string_field(2)
    """The password to sign up with."""


@dataclass(eq=False, repr=False)
class SignOutRequest(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class IdentityProvider(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the identityProvider.
     Format: identityProviders/{id}
    """

    type: "IdentityProviderType" = betterproto.enum_field(2)
    title: str = betterproto.string_field(3)
    identifier_filter: str = betterproto.string_field(4)
    config: "IdentityProviderConfig" = betterproto.message_field(5)


@dataclass(eq=False, repr=False)
class IdentityProviderConfig(betterproto.Message):
    oauth2_config: "OAuth2Config" = betterproto.message_field(1, group="config")


@dataclass(eq=False, repr=False)
class FieldMapping(betterproto.Message):
    identifier: str = betterproto.string_field(1)
    display_name: str = betterproto.string_field(2)
    email: str = betterproto.string_field(3)


@dataclass(eq=False, repr=False)
class OAuth2Config(betterproto.Message):
    client_id: str = betterproto.string_field(1)
    client_secret: str = betterproto.string_field(2)
    auth_url: str = betterproto.string_field(3)
    token_url: str = betterproto.string_field(4)
    user_info_url: str = betterproto.string_field(5)
    scopes: List[str] = betterproto.string_field(6)
    field_mapping: "FieldMapping" = betterproto.message_field(7)


@dataclass(eq=False, repr=False)
class ListIdentityProvidersRequest(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class ListIdentityProvidersResponse(betterproto.Message):
    identity_providers: List["IdentityProvider"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class GetIdentityProviderRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the identityProvider to get.
     Format: identityProviders/{id}
    """


@dataclass(eq=False, repr=False)
class CreateIdentityProviderRequest(betterproto.Message):
    identity_provider: "IdentityProvider" = betterproto.message_field(1)
    """The identityProvider to create."""


@dataclass(eq=False, repr=False)
class UpdateIdentityProviderRequest(betterproto.Message):
    identity_provider: "IdentityProvider" = betterproto.message_field(1)
    """The identityProvider to update."""

    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )
    """
    The update mask applies to the resource. Only the top level fields of
     IdentityProvider are supported.
    """


@dataclass(eq=False, repr=False)
class DeleteIdentityProviderRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the identityProvider to delete.
     Format: identityProviders/{id}
    """


@dataclass(eq=False, repr=False)
class Inbox(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the inbox.
     Format: inboxes/{id}
    """

    sender: str = betterproto.string_field(2)
    """Format: users/{id}"""

    receiver: str = betterproto.string_field(3)
    """Format: users/{id}"""

    status: "InboxStatus" = betterproto.enum_field(4)
    create_time: datetime = betterproto.message_field(5)
    type: "InboxType" = betterproto.enum_field(6)
    activity_id: Optional[int] = betterproto.int32_field(7, optional=True)


@dataclass(eq=False, repr=False)
class ListInboxesRequest(betterproto.Message):
    user: str = betterproto.string_field(1)
    """Format: users/{id}"""


@dataclass(eq=False, repr=False)
class ListInboxesResponse(betterproto.Message):
    inboxes: List["Inbox"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class UpdateInboxRequest(betterproto.Message):
    inbox: "Inbox" = betterproto.message_field(1)
    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )


@dataclass(eq=False, repr=False)
class DeleteInboxRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the inbox to delete.
     Format: inboxes/{id}
    """


@dataclass(eq=False, repr=False)
class ParseMarkdownRequest(betterproto.Message):
    markdown: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class ParseMarkdownResponse(betterproto.Message):
    nodes: List["Node"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class RestoreMarkdownRequest(betterproto.Message):
    nodes: List["Node"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class RestoreMarkdownResponse(betterproto.Message):
    markdown: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class GetLinkMetadataRequest(betterproto.Message):
    link: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class LinkMetadata(betterproto.Message):
    title: str = betterproto.string_field(1)
    description: str = betterproto.string_field(2)
    image: str = betterproto.string_field(3)


@dataclass(eq=False, repr=False)
class Node(betterproto.Message):
    type: "NodeType" = betterproto.enum_field(1)
    line_break_node: "LineBreakNode" = betterproto.message_field(2, group="node")
    paragraph_node: "ParagraphNode" = betterproto.message_field(3, group="node")
    code_block_node: "CodeBlockNode" = betterproto.message_field(4, group="node")
    heading_node: "HeadingNode" = betterproto.message_field(5, group="node")
    horizontal_rule_node: "HorizontalRuleNode" = betterproto.message_field(
        6, group="node"
    )
    blockquote_node: "BlockquoteNode" = betterproto.message_field(7, group="node")
    ordered_list_node: "OrderedListNode" = betterproto.message_field(8, group="node")
    unordered_list_node: "UnorderedListNode" = betterproto.message_field(
        9, group="node"
    )
    task_list_node: "TaskListNode" = betterproto.message_field(10, group="node")
    math_block_node: "MathBlockNode" = betterproto.message_field(11, group="node")
    table_node: "TableNode" = betterproto.message_field(12, group="node")
    embedded_content_node: "EmbeddedContentNode" = betterproto.message_field(
        13, group="node"
    )
    text_node: "TextNode" = betterproto.message_field(14, group="node")
    bold_node: "BoldNode" = betterproto.message_field(15, group="node")
    italic_node: "ItalicNode" = betterproto.message_field(16, group="node")
    bold_italic_node: "BoldItalicNode" = betterproto.message_field(17, group="node")
    code_node: "CodeNode" = betterproto.message_field(18, group="node")
    image_node: "ImageNode" = betterproto.message_field(19, group="node")
    link_node: "LinkNode" = betterproto.message_field(20, group="node")
    auto_link_node: "AutoLinkNode" = betterproto.message_field(21, group="node")
    tag_node: "TagNode" = betterproto.message_field(22, group="node")
    strikethrough_node: "StrikethroughNode" = betterproto.message_field(
        23, group="node"
    )
    escaping_character_node: "EscapingCharacterNode" = betterproto.message_field(
        24, group="node"
    )
    math_node: "MathNode" = betterproto.message_field(25, group="node")
    highlight_node: "HighlightNode" = betterproto.message_field(26, group="node")
    subscript_node: "SubscriptNode" = betterproto.message_field(27, group="node")
    superscript_node: "SuperscriptNode" = betterproto.message_field(28, group="node")
    referenced_content_node: "ReferencedContentNode" = betterproto.message_field(
        29, group="node"
    )
    spoiler_node: "SpoilerNode" = betterproto.message_field(30, group="node")


@dataclass(eq=False, repr=False)
class LineBreakNode(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class ParagraphNode(betterproto.Message):
    children: List["Node"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class CodeBlockNode(betterproto.Message):
    language: str = betterproto.string_field(1)
    content: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class HeadingNode(betterproto.Message):
    level: int = betterproto.int32_field(1)
    children: List["Node"] = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class HorizontalRuleNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class BlockquoteNode(betterproto.Message):
    children: List["Node"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class OrderedListNode(betterproto.Message):
    number: str = betterproto.string_field(1)
    indent: int = betterproto.int32_field(2)
    children: List["Node"] = betterproto.message_field(3)


@dataclass(eq=False, repr=False)
class UnorderedListNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)
    indent: int = betterproto.int32_field(2)
    children: List["Node"] = betterproto.message_field(3)


@dataclass(eq=False, repr=False)
class TaskListNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)
    indent: int = betterproto.int32_field(2)
    complete: bool = betterproto.bool_field(3)
    children: List["Node"] = betterproto.message_field(4)


@dataclass(eq=False, repr=False)
class MathBlockNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class TableNode(betterproto.Message):
    header: List[str] = betterproto.string_field(1)
    delimiter: List[str] = betterproto.string_field(2)
    rows: List["TableNodeRow"] = betterproto.message_field(3)


@dataclass(eq=False, repr=False)
class TableNodeRow(betterproto.Message):
    cells: List[str] = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class EmbeddedContentNode(betterproto.Message):
    resource_name: str = betterproto.string_field(1)
    params: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class TextNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class BoldNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)
    children: List["Node"] = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class ItalicNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)
    content: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class BoldItalicNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)
    content: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class CodeNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class ImageNode(betterproto.Message):
    alt_text: str = betterproto.string_field(1)
    url: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class LinkNode(betterproto.Message):
    text: str = betterproto.string_field(1)
    url: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class AutoLinkNode(betterproto.Message):
    url: str = betterproto.string_field(1)
    is_raw_text: bool = betterproto.bool_field(2)


@dataclass(eq=False, repr=False)
class TagNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class StrikethroughNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class EscapingCharacterNode(betterproto.Message):
    symbol: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class MathNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class HighlightNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class SubscriptNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class SuperscriptNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class ReferencedContentNode(betterproto.Message):
    resource_name: str = betterproto.string_field(1)
    params: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class SpoilerNode(betterproto.Message):
    content: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class MemoRelation(betterproto.Message):
    memo: str = betterproto.string_field(1)
    """
    The name of memo.
     Format: "memos/{uid}"
    """

    related_memo: str = betterproto.string_field(2)
    """
    The name of related memo.
     Format: "memos/{uid}"
    """

    type: "MemoRelationType" = betterproto.enum_field(3)


@dataclass(eq=False, repr=False)
class Reaction(betterproto.Message):
    id: int = betterproto.int32_field(1)
    creator: str = betterproto.string_field(2)
    """
    The name of the creator.
     Format: users/{id}
    """

    content_id: str = betterproto.string_field(3)
    reaction_type: "ReactionType" = betterproto.enum_field(4)


@dataclass(eq=False, repr=False)
class Resource(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the resource.
     Format: resources/{id}
     id is the system generated unique identifier.
    """

    uid: str = betterproto.string_field(2)
    """The user defined id of the resource."""

    create_time: datetime = betterproto.message_field(3)
    filename: str = betterproto.string_field(4)
    content: bytes = betterproto.bytes_field(5)
    external_link: str = betterproto.string_field(6)
    type: str = betterproto.string_field(7)
    size: int = betterproto.int64_field(8)
    memo: Optional[str] = betterproto.string_field(9, optional=True)
    """
    The related memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class CreateResourceRequest(betterproto.Message):
    resource: "Resource" = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class ListResourcesRequest(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class ListResourcesResponse(betterproto.Message):
    resources: List["Resource"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class SearchResourcesRequest(betterproto.Message):
    filter: str = betterproto.string_field(1)


@dataclass(eq=False, repr=False)
class SearchResourcesResponse(betterproto.Message):
    resources: List["Resource"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class GetResourceRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the resource.
     Format: resources/{id}
     id is the system generated unique identifier.
    """


@dataclass(eq=False, repr=False)
class GetResourceBinaryRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the resource.
     Format: resources/{id}
     id is the system generated unique identifier.
    """

    filename: str = betterproto.string_field(2)
    """The filename of the resource. Mainly used for downloading."""


@dataclass(eq=False, repr=False)
class UpdateResourceRequest(betterproto.Message):
    resource: "Resource" = betterproto.message_field(1)
    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )


@dataclass(eq=False, repr=False)
class DeleteResourceRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the resource.
     Format: resources/{id}
     id is the system generated unique identifier.
    """


@dataclass(eq=False, repr=False)
class Memo(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
     id is the system generated id.
    """

    uid: str = betterproto.string_field(2)
    """The user defined id of the memo."""

    row_status: "RowStatus" = betterproto.enum_field(3)
    creator: str = betterproto.string_field(4)
    """
    The name of the creator.
     Format: users/{id}
    """

    create_time: datetime = betterproto.message_field(5)
    update_time: datetime = betterproto.message_field(6)
    display_time: datetime = betterproto.message_field(7)
    content: str = betterproto.string_field(8)
    nodes: List["Node"] = betterproto.message_field(9)
    visibility: "Visibility" = betterproto.enum_field(10)
    tags: List[str] = betterproto.string_field(11)
    pinned: bool = betterproto.bool_field(12)
    parent_id: Optional[int] = betterproto.int32_field(13, optional=True)
    resources: List["Resource"] = betterproto.message_field(14)
    relations: List["MemoRelation"] = betterproto.message_field(15)
    reactions: List["Reaction"] = betterproto.message_field(16)
    property: "MemoProperty" = betterproto.message_field(17)
    parent: Optional[str] = betterproto.string_field(18, optional=True)
    """
    The name of the parent memo.
     Format: memos/{id}
    """

    def __post_init__(self) -> None:
        super().__post_init__()
        if self.is_set("parent_id"):
            warnings.warn("Memo.parent_id is deprecated", DeprecationWarning)


@dataclass(eq=False, repr=False)
class MemoProperty(betterproto.Message):
    tags: List[str] = betterproto.string_field(1)
    has_link: bool = betterproto.bool_field(2)
    has_task_list: bool = betterproto.bool_field(3)
    has_code: bool = betterproto.bool_field(4)
    has_incomplete_tasks: bool = betterproto.bool_field(5)


@dataclass(eq=False, repr=False)
class CreateMemoRequest(betterproto.Message):
    content: str = betterproto.string_field(1)
    visibility: "Visibility" = betterproto.enum_field(2)


@dataclass(eq=False, repr=False)
class ListMemosRequest(betterproto.Message):
    page_size: int = betterproto.int32_field(1)
    """The maximum number of memos to return."""

    page_token: str = betterproto.string_field(2)
    """
    A page token, received from a previous `ListMemos` call.
     Provide this to retrieve the subsequent page.
    """

    filter: str = betterproto.string_field(3)
    """
    Filter is used to filter memos returned in the list.
     Format: "creator == 'users/{uid}' && visibilities == ['PUBLIC', 'PROTECTED']"
    """


@dataclass(eq=False, repr=False)
class ListMemosResponse(betterproto.Message):
    memos: List["Memo"] = betterproto.message_field(1)
    next_page_token: str = betterproto.string_field(2)
    """
    A token, which can be sent as `page_token` to retrieve the next page.
     If this field is omitted, there are no subsequent pages.
    """


@dataclass(eq=False, repr=False)
class SearchMemosRequest(betterproto.Message):
    filter: str = betterproto.string_field(1)
    """
    Filter is used to filter memos returned.
     Format: "creator == 'users/{uid}' && visibilities == ['PUBLIC', 'PROTECTED']"
    """


@dataclass(eq=False, repr=False)
class SearchMemosResponse(betterproto.Message):
    memos: List["Memo"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class GetMemoRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class UpdateMemoRequest(betterproto.Message):
    memo: "Memo" = betterproto.message_field(1)
    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )


@dataclass(eq=False, repr=False)
class DeleteMemoRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class ExportMemosRequest(betterproto.Message):
    filter: str = betterproto.string_field(1)
    """Same as ListMemosRequest.filter"""


@dataclass(eq=False, repr=False)
class ExportMemosResponse(betterproto.Message):
    content: bytes = betterproto.bytes_field(1)


@dataclass(eq=False, repr=False)
class ListMemoPropertiesRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}. Use "memos/-" to list all properties.
    """


@dataclass(eq=False, repr=False)
class ListMemoPropertiesResponse(betterproto.Message):
    properties: List["MemoProperty"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class RebuildMemoPropertyRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}. Use "memos/-" to rebuild all memos.
    """


@dataclass(eq=False, repr=False)
class ListMemoTagsRequest(betterproto.Message):
    parent: str = betterproto.string_field(1)
    """
    The parent, who owns the tags.
     Format: memos/{id}. Use "memos/-" to list all tags.
    """

    filter: str = betterproto.string_field(2)
    """
    Filter is used to filter memos.
     Format: "creator == 'users/{uid}' && visibilities == ['PUBLIC', 'PROTECTED']"
    """


@dataclass(eq=False, repr=False)
class ListMemoTagsResponse(betterproto.Message):
    tag_amounts: Dict[str, int] = betterproto.map_field(
        1, betterproto.TYPE_STRING, betterproto.TYPE_INT32
    )
    """
    tag_amounts is the amount of tags.
     key is the tag name. e.g. "tag1".
     value is the amount of the tag.
    """


@dataclass(eq=False, repr=False)
class RenameMemoTagRequest(betterproto.Message):
    parent: str = betterproto.string_field(1)
    """
    The parent, who owns the tags.
     Format: memos/{id}. Use "memos/-" to rename all tags.
    """

    old_tag: str = betterproto.string_field(2)
    new_tag: str = betterproto.string_field(3)


@dataclass(eq=False, repr=False)
class DeleteMemoTagRequest(betterproto.Message):
    parent: str = betterproto.string_field(1)
    """
    The parent, who owns the tags.
     Format: memos/{id}. Use "memos/-" to delete all tags.
    """

    tag: str = betterproto.string_field(2)
    delete_related_memos: bool = betterproto.bool_field(3)


@dataclass(eq=False, repr=False)
class SetMemoResourcesRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """

    resources: List["Resource"] = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class ListMemoResourcesRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class ListMemoResourcesResponse(betterproto.Message):
    resources: List["Resource"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class SetMemoRelationsRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """

    relations: List["MemoRelation"] = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class ListMemoRelationsRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class ListMemoRelationsResponse(betterproto.Message):
    relations: List["MemoRelation"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class CreateMemoCommentRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """

    comment: "CreateMemoRequest" = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class ListMemoCommentsRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class ListMemoCommentsResponse(betterproto.Message):
    memos: List["Memo"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class GetUserMemosStatsRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    name is the name of the user to get stats for.
     Format: users/{id}
    """

    timezone: str = betterproto.string_field(2)
    """
    timezone location
     Format: uses tz identifier
     https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
    """

    filter: str = betterproto.string_field(3)
    """Same as ListMemosRequest.filter"""


@dataclass(eq=False, repr=False)
class GetUserMemosStatsResponse(betterproto.Message):
    stats: Dict[str, int] = betterproto.map_field(
        1, betterproto.TYPE_STRING, betterproto.TYPE_INT32
    )
    """
    stats is the stats of memo creating/updating activities.
     key is the year-month-day string. e.g. "2020-01-01".
    """


@dataclass(eq=False, repr=False)
class ListMemoReactionsRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """


@dataclass(eq=False, repr=False)
class ListMemoReactionsResponse(betterproto.Message):
    reactions: List["Reaction"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class UpsertMemoReactionRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The name of the memo.
     Format: memos/{id}
    """

    reaction: "Reaction" = betterproto.message_field(2)


@dataclass(eq=False, repr=False)
class DeleteMemoReactionRequest(betterproto.Message):
    reaction_id: int = betterproto.int32_field(1)


@dataclass(eq=False, repr=False)
class Webhook(betterproto.Message):
    id: int = betterproto.int32_field(1)
    creator_id: int = betterproto.int32_field(2)
    create_time: datetime = betterproto.message_field(3)
    update_time: datetime = betterproto.message_field(4)
    row_status: "RowStatus" = betterproto.enum_field(5)
    name: str = betterproto.string_field(6)
    url: str = betterproto.string_field(7)


@dataclass(eq=False, repr=False)
class CreateWebhookRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    url: str = betterproto.string_field(2)


@dataclass(eq=False, repr=False)
class GetWebhookRequest(betterproto.Message):
    id: int = betterproto.int32_field(1)


@dataclass(eq=False, repr=False)
class ListWebhooksRequest(betterproto.Message):
    creator_id: int = betterproto.int32_field(1)


@dataclass(eq=False, repr=False)
class ListWebhooksResponse(betterproto.Message):
    webhooks: List["Webhook"] = betterproto.message_field(1)


@dataclass(eq=False, repr=False)
class UpdateWebhookRequest(betterproto.Message):
    webhook: "Webhook" = betterproto.message_field(1)
    update_mask: "betterproto_lib_google_protobuf.FieldMask" = (
        betterproto.message_field(2)
    )


@dataclass(eq=False, repr=False)
class DeleteWebhookRequest(betterproto.Message):
    id: int = betterproto.int32_field(1)


@dataclass(eq=False, repr=False)
class WebhookRequestPayload(betterproto.Message):
    url: str = betterproto.string_field(1)
    activity_type: str = betterproto.string_field(2)
    creator_id: int = betterproto.int32_field(3)
    create_time: datetime = betterproto.message_field(4)
    memo: "Memo" = betterproto.message_field(5)


@dataclass(eq=False, repr=False)
class WorkspaceProfile(betterproto.Message):
    owner: str = betterproto.string_field(1)
    """
    The name of instance owner.
     Format: "users/{id}"
    """

    version: str = betterproto.string_field(2)
    """version is the current version of instance"""

    mode: str = betterproto.string_field(3)
    """mode is the instance mode (e.g. "prod", "dev" or "demo")."""


@dataclass(eq=False, repr=False)
class GetWorkspaceProfileRequest(betterproto.Message):
    pass


@dataclass(eq=False, repr=False)
class WorkspaceSetting(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    name is the name of the setting.
     Format: settings/{setting}
    """

    general_setting: "WorkspaceGeneralSetting" = betterproto.message_field(
        2, group="value"
    )
    storage_setting: "WorkspaceStorageSetting" = betterproto.message_field(
        3, group="value"
    )
    memo_related_setting: "WorkspaceMemoRelatedSetting" = betterproto.message_field(
        4, group="value"
    )


@dataclass(eq=False, repr=False)
class WorkspaceGeneralSetting(betterproto.Message):
    disallow_signup: bool = betterproto.bool_field(1)
    """disallow_signup is the flag to disallow signup."""

    disallow_password_login: bool = betterproto.bool_field(2)
    """disallow_password_login is the flag to disallow password login."""

    additional_script: str = betterproto.string_field(3)
    """additional_script is the additional script."""

    additional_style: str = betterproto.string_field(4)
    """additional_style is the additional style."""

    custom_profile: "WorkspaceCustomProfile" = betterproto.message_field(5)
    """custom_profile is the custom profile."""


@dataclass(eq=False, repr=False)
class WorkspaceCustomProfile(betterproto.Message):
    title: str = betterproto.string_field(1)
    description: str = betterproto.string_field(2)
    logo_url: str = betterproto.string_field(3)
    locale: str = betterproto.string_field(4)
    appearance: str = betterproto.string_field(5)


@dataclass(eq=False, repr=False)
class WorkspaceStorageSetting(betterproto.Message):
    storage_type: "WorkspaceStorageSettingStorageType" = betterproto.enum_field(1)
    """storage_type is the storage type."""

    filepath_template: str = betterproto.string_field(2)
    """
    The template of file path.
     e.g. assets/{timestamp}_{filename}
    """

    upload_size_limit_mb: int = betterproto.int64_field(3)
    """The max upload size in megabytes."""

    s3_config: "WorkspaceStorageSettingS3Config" = betterproto.message_field(4)
    """The S3 config."""


@dataclass(eq=False, repr=False)
class WorkspaceStorageSettingS3Config(betterproto.Message):
    """
    Reference: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-go/
    """

    access_key_id: str = betterproto.string_field(1)
    access_key_secret: str = betterproto.string_field(2)
    endpoint: str = betterproto.string_field(3)
    region: str = betterproto.string_field(4)
    bucket: str = betterproto.string_field(5)


@dataclass(eq=False, repr=False)
class WorkspaceMemoRelatedSetting(betterproto.Message):
    disallow_public_visible: bool = betterproto.bool_field(1)
    """disallow_public_share disallows set memo as public visible."""

    display_with_update_time: bool = betterproto.bool_field(2)
    """display_with_update_time orders and displays memo with update time."""

    content_length_limit: int = betterproto.int32_field(3)
    """content_length_limit is the limit of content length. Unit is byte."""

    enable_auto_compact: bool = betterproto.bool_field(4)
    """enable_auto_compact enables auto compact for large content."""

    enable_double_click_edit: bool = betterproto.bool_field(5)
    """enable_double_click_edit enables editing on double click."""


@dataclass(eq=False, repr=False)
class GetWorkspaceSettingRequest(betterproto.Message):
    name: str = betterproto.string_field(1)
    """
    The resource name of the workspace setting.
     Format: settings/{setting}
    """


@dataclass(eq=False, repr=False)
class SetWorkspaceSettingRequest(betterproto.Message):
    setting: "WorkspaceSetting" = betterproto.message_field(1)
    """setting is the setting to update."""


class ActivityServiceStub(betterproto.ServiceStub):
    async def get_activity(
        self,
        get_activity_request: "GetActivityRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Activity":
        return await self._unary_unary(
            "/memos.api.v1.ActivityService/GetActivity",
            get_activity_request,
            Activity,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class UserServiceStub(betterproto.ServiceStub):
    async def list_users(
        self,
        list_users_request: "ListUsersRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListUsersResponse":
        return await self._unary_unary(
            "/memos.api.v1.UserService/ListUsers",
            list_users_request,
            ListUsersResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def search_users(
        self,
        search_users_request: "SearchUsersRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "SearchUsersResponse":
        return await self._unary_unary(
            "/memos.api.v1.UserService/SearchUsers",
            search_users_request,
            SearchUsersResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_user(
        self,
        get_user_request: "GetUserRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.UserService/GetUser",
            get_user_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_user_avatar_binary(
        self,
        get_user_avatar_binary_request: "GetUserAvatarBinaryRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "___google_api__.HttpBody":
        return await self._unary_unary(
            "/memos.api.v1.UserService/GetUserAvatarBinary",
            get_user_avatar_binary_request,
            ___google_api__.HttpBody,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def create_user(
        self,
        create_user_request: "CreateUserRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.UserService/CreateUser",
            create_user_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_user(
        self,
        update_user_request: "UpdateUserRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.UserService/UpdateUser",
            update_user_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_user(
        self,
        delete_user_request: "DeleteUserRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.UserService/DeleteUser",
            delete_user_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_user_setting(
        self,
        get_user_setting_request: "GetUserSettingRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "UserSetting":
        return await self._unary_unary(
            "/memos.api.v1.UserService/GetUserSetting",
            get_user_setting_request,
            UserSetting,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_user_setting(
        self,
        update_user_setting_request: "UpdateUserSettingRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "UserSetting":
        return await self._unary_unary(
            "/memos.api.v1.UserService/UpdateUserSetting",
            update_user_setting_request,
            UserSetting,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_user_access_tokens(
        self,
        list_user_access_tokens_request: "ListUserAccessTokensRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListUserAccessTokensResponse":
        return await self._unary_unary(
            "/memos.api.v1.UserService/ListUserAccessTokens",
            list_user_access_tokens_request,
            ListUserAccessTokensResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def create_user_access_token(
        self,
        create_user_access_token_request: "CreateUserAccessTokenRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "UserAccessToken":
        return await self._unary_unary(
            "/memos.api.v1.UserService/CreateUserAccessToken",
            create_user_access_token_request,
            UserAccessToken,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_user_access_token(
        self,
        delete_user_access_token_request: "DeleteUserAccessTokenRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.UserService/DeleteUserAccessToken",
            delete_user_access_token_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class AuthServiceStub(betterproto.ServiceStub):
    async def get_auth_status(
        self,
        get_auth_status_request: "GetAuthStatusRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.AuthService/GetAuthStatus",
            get_auth_status_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def sign_in(
        self,
        sign_in_request: "SignInRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.AuthService/SignIn",
            sign_in_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def sign_in_with_sso(
        self,
        sign_in_with_sso_request: "SignInWithSsoRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.AuthService/SignInWithSSO",
            sign_in_with_sso_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def sign_up(
        self,
        sign_up_request: "SignUpRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "User":
        return await self._unary_unary(
            "/memos.api.v1.AuthService/SignUp",
            sign_up_request,
            User,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def sign_out(
        self,
        sign_out_request: "SignOutRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.AuthService/SignOut",
            sign_out_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class IdentityProviderServiceStub(betterproto.ServiceStub):
    async def list_identity_providers(
        self,
        list_identity_providers_request: "ListIdentityProvidersRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListIdentityProvidersResponse":
        return await self._unary_unary(
            "/memos.api.v1.IdentityProviderService/ListIdentityProviders",
            list_identity_providers_request,
            ListIdentityProvidersResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_identity_provider(
        self,
        get_identity_provider_request: "GetIdentityProviderRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "IdentityProvider":
        return await self._unary_unary(
            "/memos.api.v1.IdentityProviderService/GetIdentityProvider",
            get_identity_provider_request,
            IdentityProvider,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def create_identity_provider(
        self,
        create_identity_provider_request: "CreateIdentityProviderRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "IdentityProvider":
        return await self._unary_unary(
            "/memos.api.v1.IdentityProviderService/CreateIdentityProvider",
            create_identity_provider_request,
            IdentityProvider,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_identity_provider(
        self,
        update_identity_provider_request: "UpdateIdentityProviderRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "IdentityProvider":
        return await self._unary_unary(
            "/memos.api.v1.IdentityProviderService/UpdateIdentityProvider",
            update_identity_provider_request,
            IdentityProvider,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_identity_provider(
        self,
        delete_identity_provider_request: "DeleteIdentityProviderRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.IdentityProviderService/DeleteIdentityProvider",
            delete_identity_provider_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class InboxServiceStub(betterproto.ServiceStub):
    async def list_inboxes(
        self,
        list_inboxes_request: "ListInboxesRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListInboxesResponse":
        return await self._unary_unary(
            "/memos.api.v1.InboxService/ListInboxes",
            list_inboxes_request,
            ListInboxesResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_inbox(
        self,
        update_inbox_request: "UpdateInboxRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Inbox":
        return await self._unary_unary(
            "/memos.api.v1.InboxService/UpdateInbox",
            update_inbox_request,
            Inbox,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_inbox(
        self,
        delete_inbox_request: "DeleteInboxRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.InboxService/DeleteInbox",
            delete_inbox_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class MarkdownServiceStub(betterproto.ServiceStub):
    async def parse_markdown(
        self,
        parse_markdown_request: "ParseMarkdownRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ParseMarkdownResponse":
        return await self._unary_unary(
            "/memos.api.v1.MarkdownService/ParseMarkdown",
            parse_markdown_request,
            ParseMarkdownResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def restore_markdown(
        self,
        restore_markdown_request: "RestoreMarkdownRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "RestoreMarkdownResponse":
        return await self._unary_unary(
            "/memos.api.v1.MarkdownService/RestoreMarkdown",
            restore_markdown_request,
            RestoreMarkdownResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_link_metadata(
        self,
        get_link_metadata_request: "GetLinkMetadataRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "LinkMetadata":
        return await self._unary_unary(
            "/memos.api.v1.MarkdownService/GetLinkMetadata",
            get_link_metadata_request,
            LinkMetadata,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class ResourceServiceStub(betterproto.ServiceStub):
    async def create_resource(
        self,
        create_resource_request: "CreateResourceRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Resource":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/CreateResource",
            create_resource_request,
            Resource,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_resources(
        self,
        list_resources_request: "ListResourcesRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListResourcesResponse":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/ListResources",
            list_resources_request,
            ListResourcesResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def search_resources(
        self,
        search_resources_request: "SearchResourcesRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "SearchResourcesResponse":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/SearchResources",
            search_resources_request,
            SearchResourcesResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_resource(
        self,
        get_resource_request: "GetResourceRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Resource":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/GetResource",
            get_resource_request,
            Resource,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_resource_binary(
        self,
        get_resource_binary_request: "GetResourceBinaryRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "___google_api__.HttpBody":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/GetResourceBinary",
            get_resource_binary_request,
            ___google_api__.HttpBody,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_resource(
        self,
        update_resource_request: "UpdateResourceRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Resource":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/UpdateResource",
            update_resource_request,
            Resource,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_resource(
        self,
        delete_resource_request: "DeleteResourceRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.ResourceService/DeleteResource",
            delete_resource_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class MemoServiceStub(betterproto.ServiceStub):
    async def create_memo(
        self,
        create_memo_request: "CreateMemoRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Memo":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/CreateMemo",
            create_memo_request,
            Memo,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memos(
        self,
        list_memos_request: "ListMemosRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemosResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemos",
            list_memos_request,
            ListMemosResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def search_memos(
        self,
        search_memos_request: "SearchMemosRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "SearchMemosResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/SearchMemos",
            search_memos_request,
            SearchMemosResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_memo(
        self,
        get_memo_request: "GetMemoRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Memo":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/GetMemo",
            get_memo_request,
            Memo,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_memo(
        self,
        update_memo_request: "UpdateMemoRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Memo":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/UpdateMemo",
            update_memo_request,
            Memo,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_memo(
        self,
        delete_memo_request: "DeleteMemoRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/DeleteMemo",
            delete_memo_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def export_memos(
        self,
        export_memos_request: "ExportMemosRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ExportMemosResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ExportMemos",
            export_memos_request,
            ExportMemosResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memo_properties(
        self,
        list_memo_properties_request: "ListMemoPropertiesRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemoPropertiesResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemoProperties",
            list_memo_properties_request,
            ListMemoPropertiesResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def rebuild_memo_property(
        self,
        rebuild_memo_property_request: "RebuildMemoPropertyRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/RebuildMemoProperty",
            rebuild_memo_property_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memo_tags(
        self,
        list_memo_tags_request: "ListMemoTagsRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemoTagsResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemoTags",
            list_memo_tags_request,
            ListMemoTagsResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def rename_memo_tag(
        self,
        rename_memo_tag_request: "RenameMemoTagRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/RenameMemoTag",
            rename_memo_tag_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_memo_tag(
        self,
        delete_memo_tag_request: "DeleteMemoTagRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/DeleteMemoTag",
            delete_memo_tag_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def set_memo_resources(
        self,
        set_memo_resources_request: "SetMemoResourcesRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/SetMemoResources",
            set_memo_resources_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memo_resources(
        self,
        list_memo_resources_request: "ListMemoResourcesRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemoResourcesResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemoResources",
            list_memo_resources_request,
            ListMemoResourcesResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def set_memo_relations(
        self,
        set_memo_relations_request: "SetMemoRelationsRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/SetMemoRelations",
            set_memo_relations_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memo_relations(
        self,
        list_memo_relations_request: "ListMemoRelationsRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemoRelationsResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemoRelations",
            list_memo_relations_request,
            ListMemoRelationsResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def create_memo_comment(
        self,
        create_memo_comment_request: "CreateMemoCommentRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Memo":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/CreateMemoComment",
            create_memo_comment_request,
            Memo,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memo_comments(
        self,
        list_memo_comments_request: "ListMemoCommentsRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemoCommentsResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemoComments",
            list_memo_comments_request,
            ListMemoCommentsResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_user_memos_stats(
        self,
        get_user_memos_stats_request: "GetUserMemosStatsRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "GetUserMemosStatsResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/GetUserMemosStats",
            get_user_memos_stats_request,
            GetUserMemosStatsResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_memo_reactions(
        self,
        list_memo_reactions_request: "ListMemoReactionsRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListMemoReactionsResponse":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/ListMemoReactions",
            list_memo_reactions_request,
            ListMemoReactionsResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def upsert_memo_reaction(
        self,
        upsert_memo_reaction_request: "UpsertMemoReactionRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Reaction":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/UpsertMemoReaction",
            upsert_memo_reaction_request,
            Reaction,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_memo_reaction(
        self,
        delete_memo_reaction_request: "DeleteMemoReactionRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.MemoService/DeleteMemoReaction",
            delete_memo_reaction_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class WebhookServiceStub(betterproto.ServiceStub):
    async def create_webhook(
        self,
        create_webhook_request: "CreateWebhookRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Webhook":
        return await self._unary_unary(
            "/memos.api.v1.WebhookService/CreateWebhook",
            create_webhook_request,
            Webhook,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def get_webhook(
        self,
        get_webhook_request: "GetWebhookRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Webhook":
        return await self._unary_unary(
            "/memos.api.v1.WebhookService/GetWebhook",
            get_webhook_request,
            Webhook,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def list_webhooks(
        self,
        list_webhooks_request: "ListWebhooksRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "ListWebhooksResponse":
        return await self._unary_unary(
            "/memos.api.v1.WebhookService/ListWebhooks",
            list_webhooks_request,
            ListWebhooksResponse,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def update_webhook(
        self,
        update_webhook_request: "UpdateWebhookRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "Webhook":
        return await self._unary_unary(
            "/memos.api.v1.WebhookService/UpdateWebhook",
            update_webhook_request,
            Webhook,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def delete_webhook(
        self,
        delete_webhook_request: "DeleteWebhookRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "betterproto_lib_google_protobuf.Empty":
        return await self._unary_unary(
            "/memos.api.v1.WebhookService/DeleteWebhook",
            delete_webhook_request,
            betterproto_lib_google_protobuf.Empty,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class WorkspaceServiceStub(betterproto.ServiceStub):
    async def get_workspace_profile(
        self,
        get_workspace_profile_request: "GetWorkspaceProfileRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "WorkspaceProfile":
        return await self._unary_unary(
            "/memos.api.v1.WorkspaceService/GetWorkspaceProfile",
            get_workspace_profile_request,
            WorkspaceProfile,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class WorkspaceSettingServiceStub(betterproto.ServiceStub):
    async def get_workspace_setting(
        self,
        get_workspace_setting_request: "GetWorkspaceSettingRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "WorkspaceSetting":
        return await self._unary_unary(
            "/memos.api.v1.WorkspaceSettingService/GetWorkspaceSetting",
            get_workspace_setting_request,
            WorkspaceSetting,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )

    async def set_workspace_setting(
        self,
        set_workspace_setting_request: "SetWorkspaceSettingRequest",
        *,
        timeout: Optional[float] = None,
        deadline: Optional["Deadline"] = None,
        metadata: Optional["MetadataLike"] = None
    ) -> "WorkspaceSetting":
        return await self._unary_unary(
            "/memos.api.v1.WorkspaceSettingService/SetWorkspaceSetting",
            set_workspace_setting_request,
            WorkspaceSetting,
            timeout=timeout,
            deadline=deadline,
            metadata=metadata,
        )


class ActivityServiceBase(ServiceBase):

    async def get_activity(
        self, get_activity_request: "GetActivityRequest"
    ) -> "Activity":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_get_activity(
        self, stream: "grpclib.server.Stream[GetActivityRequest, Activity]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_activity(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.ActivityService/GetActivity": grpclib.const.Handler(
                self.__rpc_get_activity,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetActivityRequest,
                Activity,
            ),
        }


class UserServiceBase(ServiceBase):

    async def list_users(
        self, list_users_request: "ListUsersRequest"
    ) -> "ListUsersResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def search_users(
        self, search_users_request: "SearchUsersRequest"
    ) -> "SearchUsersResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_user(self, get_user_request: "GetUserRequest") -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_user_avatar_binary(
        self, get_user_avatar_binary_request: "GetUserAvatarBinaryRequest"
    ) -> "___google_api__.HttpBody":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def create_user(self, create_user_request: "CreateUserRequest") -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_user(self, update_user_request: "UpdateUserRequest") -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_user(
        self, delete_user_request: "DeleteUserRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_user_setting(
        self, get_user_setting_request: "GetUserSettingRequest"
    ) -> "UserSetting":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_user_setting(
        self, update_user_setting_request: "UpdateUserSettingRequest"
    ) -> "UserSetting":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_user_access_tokens(
        self, list_user_access_tokens_request: "ListUserAccessTokensRequest"
    ) -> "ListUserAccessTokensResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def create_user_access_token(
        self, create_user_access_token_request: "CreateUserAccessTokenRequest"
    ) -> "UserAccessToken":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_user_access_token(
        self, delete_user_access_token_request: "DeleteUserAccessTokenRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_list_users(
        self, stream: "grpclib.server.Stream[ListUsersRequest, ListUsersResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_users(request)
        await stream.send_message(response)

    async def __rpc_search_users(
        self, stream: "grpclib.server.Stream[SearchUsersRequest, SearchUsersResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.search_users(request)
        await stream.send_message(response)

    async def __rpc_get_user(
        self, stream: "grpclib.server.Stream[GetUserRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_user(request)
        await stream.send_message(response)

    async def __rpc_get_user_avatar_binary(
        self,
        stream: "grpclib.server.Stream[GetUserAvatarBinaryRequest, ___google_api__.HttpBody]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_user_avatar_binary(request)
        await stream.send_message(response)

    async def __rpc_create_user(
        self, stream: "grpclib.server.Stream[CreateUserRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_user(request)
        await stream.send_message(response)

    async def __rpc_update_user(
        self, stream: "grpclib.server.Stream[UpdateUserRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_user(request)
        await stream.send_message(response)

    async def __rpc_delete_user(
        self,
        stream: "grpclib.server.Stream[DeleteUserRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_user(request)
        await stream.send_message(response)

    async def __rpc_get_user_setting(
        self, stream: "grpclib.server.Stream[GetUserSettingRequest, UserSetting]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_user_setting(request)
        await stream.send_message(response)

    async def __rpc_update_user_setting(
        self, stream: "grpclib.server.Stream[UpdateUserSettingRequest, UserSetting]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_user_setting(request)
        await stream.send_message(response)

    async def __rpc_list_user_access_tokens(
        self,
        stream: "grpclib.server.Stream[ListUserAccessTokensRequest, ListUserAccessTokensResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_user_access_tokens(request)
        await stream.send_message(response)

    async def __rpc_create_user_access_token(
        self,
        stream: "grpclib.server.Stream[CreateUserAccessTokenRequest, UserAccessToken]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_user_access_token(request)
        await stream.send_message(response)

    async def __rpc_delete_user_access_token(
        self,
        stream: "grpclib.server.Stream[DeleteUserAccessTokenRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_user_access_token(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.UserService/ListUsers": grpclib.const.Handler(
                self.__rpc_list_users,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListUsersRequest,
                ListUsersResponse,
            ),
            "/memos.api.v1.UserService/SearchUsers": grpclib.const.Handler(
                self.__rpc_search_users,
                grpclib.const.Cardinality.UNARY_UNARY,
                SearchUsersRequest,
                SearchUsersResponse,
            ),
            "/memos.api.v1.UserService/GetUser": grpclib.const.Handler(
                self.__rpc_get_user,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetUserRequest,
                User,
            ),
            "/memos.api.v1.UserService/GetUserAvatarBinary": grpclib.const.Handler(
                self.__rpc_get_user_avatar_binary,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetUserAvatarBinaryRequest,
                ___google_api__.HttpBody,
            ),
            "/memos.api.v1.UserService/CreateUser": grpclib.const.Handler(
                self.__rpc_create_user,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateUserRequest,
                User,
            ),
            "/memos.api.v1.UserService/UpdateUser": grpclib.const.Handler(
                self.__rpc_update_user,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateUserRequest,
                User,
            ),
            "/memos.api.v1.UserService/DeleteUser": grpclib.const.Handler(
                self.__rpc_delete_user,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteUserRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.UserService/GetUserSetting": grpclib.const.Handler(
                self.__rpc_get_user_setting,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetUserSettingRequest,
                UserSetting,
            ),
            "/memos.api.v1.UserService/UpdateUserSetting": grpclib.const.Handler(
                self.__rpc_update_user_setting,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateUserSettingRequest,
                UserSetting,
            ),
            "/memos.api.v1.UserService/ListUserAccessTokens": grpclib.const.Handler(
                self.__rpc_list_user_access_tokens,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListUserAccessTokensRequest,
                ListUserAccessTokensResponse,
            ),
            "/memos.api.v1.UserService/CreateUserAccessToken": grpclib.const.Handler(
                self.__rpc_create_user_access_token,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateUserAccessTokenRequest,
                UserAccessToken,
            ),
            "/memos.api.v1.UserService/DeleteUserAccessToken": grpclib.const.Handler(
                self.__rpc_delete_user_access_token,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteUserAccessTokenRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class AuthServiceBase(ServiceBase):

    async def get_auth_status(
        self, get_auth_status_request: "GetAuthStatusRequest"
    ) -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def sign_in(self, sign_in_request: "SignInRequest") -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def sign_in_with_sso(
        self, sign_in_with_sso_request: "SignInWithSsoRequest"
    ) -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def sign_up(self, sign_up_request: "SignUpRequest") -> "User":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def sign_out(
        self, sign_out_request: "SignOutRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_get_auth_status(
        self, stream: "grpclib.server.Stream[GetAuthStatusRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_auth_status(request)
        await stream.send_message(response)

    async def __rpc_sign_in(
        self, stream: "grpclib.server.Stream[SignInRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.sign_in(request)
        await stream.send_message(response)

    async def __rpc_sign_in_with_sso(
        self, stream: "grpclib.server.Stream[SignInWithSsoRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.sign_in_with_sso(request)
        await stream.send_message(response)

    async def __rpc_sign_up(
        self, stream: "grpclib.server.Stream[SignUpRequest, User]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.sign_up(request)
        await stream.send_message(response)

    async def __rpc_sign_out(
        self,
        stream: "grpclib.server.Stream[SignOutRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.sign_out(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.AuthService/GetAuthStatus": grpclib.const.Handler(
                self.__rpc_get_auth_status,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetAuthStatusRequest,
                User,
            ),
            "/memos.api.v1.AuthService/SignIn": grpclib.const.Handler(
                self.__rpc_sign_in,
                grpclib.const.Cardinality.UNARY_UNARY,
                SignInRequest,
                User,
            ),
            "/memos.api.v1.AuthService/SignInWithSSO": grpclib.const.Handler(
                self.__rpc_sign_in_with_sso,
                grpclib.const.Cardinality.UNARY_UNARY,
                SignInWithSsoRequest,
                User,
            ),
            "/memos.api.v1.AuthService/SignUp": grpclib.const.Handler(
                self.__rpc_sign_up,
                grpclib.const.Cardinality.UNARY_UNARY,
                SignUpRequest,
                User,
            ),
            "/memos.api.v1.AuthService/SignOut": grpclib.const.Handler(
                self.__rpc_sign_out,
                grpclib.const.Cardinality.UNARY_UNARY,
                SignOutRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class IdentityProviderServiceBase(ServiceBase):

    async def list_identity_providers(
        self, list_identity_providers_request: "ListIdentityProvidersRequest"
    ) -> "ListIdentityProvidersResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_identity_provider(
        self, get_identity_provider_request: "GetIdentityProviderRequest"
    ) -> "IdentityProvider":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def create_identity_provider(
        self, create_identity_provider_request: "CreateIdentityProviderRequest"
    ) -> "IdentityProvider":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_identity_provider(
        self, update_identity_provider_request: "UpdateIdentityProviderRequest"
    ) -> "IdentityProvider":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_identity_provider(
        self, delete_identity_provider_request: "DeleteIdentityProviderRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_list_identity_providers(
        self,
        stream: "grpclib.server.Stream[ListIdentityProvidersRequest, ListIdentityProvidersResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_identity_providers(request)
        await stream.send_message(response)

    async def __rpc_get_identity_provider(
        self,
        stream: "grpclib.server.Stream[GetIdentityProviderRequest, IdentityProvider]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_identity_provider(request)
        await stream.send_message(response)

    async def __rpc_create_identity_provider(
        self,
        stream: "grpclib.server.Stream[CreateIdentityProviderRequest, IdentityProvider]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_identity_provider(request)
        await stream.send_message(response)

    async def __rpc_update_identity_provider(
        self,
        stream: "grpclib.server.Stream[UpdateIdentityProviderRequest, IdentityProvider]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_identity_provider(request)
        await stream.send_message(response)

    async def __rpc_delete_identity_provider(
        self,
        stream: "grpclib.server.Stream[DeleteIdentityProviderRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_identity_provider(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.IdentityProviderService/ListIdentityProviders": grpclib.const.Handler(
                self.__rpc_list_identity_providers,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListIdentityProvidersRequest,
                ListIdentityProvidersResponse,
            ),
            "/memos.api.v1.IdentityProviderService/GetIdentityProvider": grpclib.const.Handler(
                self.__rpc_get_identity_provider,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetIdentityProviderRequest,
                IdentityProvider,
            ),
            "/memos.api.v1.IdentityProviderService/CreateIdentityProvider": grpclib.const.Handler(
                self.__rpc_create_identity_provider,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateIdentityProviderRequest,
                IdentityProvider,
            ),
            "/memos.api.v1.IdentityProviderService/UpdateIdentityProvider": grpclib.const.Handler(
                self.__rpc_update_identity_provider,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateIdentityProviderRequest,
                IdentityProvider,
            ),
            "/memos.api.v1.IdentityProviderService/DeleteIdentityProvider": grpclib.const.Handler(
                self.__rpc_delete_identity_provider,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteIdentityProviderRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class InboxServiceBase(ServiceBase):

    async def list_inboxes(
        self, list_inboxes_request: "ListInboxesRequest"
    ) -> "ListInboxesResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_inbox(self, update_inbox_request: "UpdateInboxRequest") -> "Inbox":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_inbox(
        self, delete_inbox_request: "DeleteInboxRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_list_inboxes(
        self, stream: "grpclib.server.Stream[ListInboxesRequest, ListInboxesResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_inboxes(request)
        await stream.send_message(response)

    async def __rpc_update_inbox(
        self, stream: "grpclib.server.Stream[UpdateInboxRequest, Inbox]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_inbox(request)
        await stream.send_message(response)

    async def __rpc_delete_inbox(
        self,
        stream: "grpclib.server.Stream[DeleteInboxRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_inbox(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.InboxService/ListInboxes": grpclib.const.Handler(
                self.__rpc_list_inboxes,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListInboxesRequest,
                ListInboxesResponse,
            ),
            "/memos.api.v1.InboxService/UpdateInbox": grpclib.const.Handler(
                self.__rpc_update_inbox,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateInboxRequest,
                Inbox,
            ),
            "/memos.api.v1.InboxService/DeleteInbox": grpclib.const.Handler(
                self.__rpc_delete_inbox,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteInboxRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class MarkdownServiceBase(ServiceBase):

    async def parse_markdown(
        self, parse_markdown_request: "ParseMarkdownRequest"
    ) -> "ParseMarkdownResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def restore_markdown(
        self, restore_markdown_request: "RestoreMarkdownRequest"
    ) -> "RestoreMarkdownResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_link_metadata(
        self, get_link_metadata_request: "GetLinkMetadataRequest"
    ) -> "LinkMetadata":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_parse_markdown(
        self,
        stream: "grpclib.server.Stream[ParseMarkdownRequest, ParseMarkdownResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.parse_markdown(request)
        await stream.send_message(response)

    async def __rpc_restore_markdown(
        self,
        stream: "grpclib.server.Stream[RestoreMarkdownRequest, RestoreMarkdownResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.restore_markdown(request)
        await stream.send_message(response)

    async def __rpc_get_link_metadata(
        self, stream: "grpclib.server.Stream[GetLinkMetadataRequest, LinkMetadata]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_link_metadata(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.MarkdownService/ParseMarkdown": grpclib.const.Handler(
                self.__rpc_parse_markdown,
                grpclib.const.Cardinality.UNARY_UNARY,
                ParseMarkdownRequest,
                ParseMarkdownResponse,
            ),
            "/memos.api.v1.MarkdownService/RestoreMarkdown": grpclib.const.Handler(
                self.__rpc_restore_markdown,
                grpclib.const.Cardinality.UNARY_UNARY,
                RestoreMarkdownRequest,
                RestoreMarkdownResponse,
            ),
            "/memos.api.v1.MarkdownService/GetLinkMetadata": grpclib.const.Handler(
                self.__rpc_get_link_metadata,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetLinkMetadataRequest,
                LinkMetadata,
            ),
        }


class ResourceServiceBase(ServiceBase):

    async def create_resource(
        self, create_resource_request: "CreateResourceRequest"
    ) -> "Resource":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_resources(
        self, list_resources_request: "ListResourcesRequest"
    ) -> "ListResourcesResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def search_resources(
        self, search_resources_request: "SearchResourcesRequest"
    ) -> "SearchResourcesResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_resource(
        self, get_resource_request: "GetResourceRequest"
    ) -> "Resource":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_resource_binary(
        self, get_resource_binary_request: "GetResourceBinaryRequest"
    ) -> "___google_api__.HttpBody":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_resource(
        self, update_resource_request: "UpdateResourceRequest"
    ) -> "Resource":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_resource(
        self, delete_resource_request: "DeleteResourceRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_create_resource(
        self, stream: "grpclib.server.Stream[CreateResourceRequest, Resource]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_resource(request)
        await stream.send_message(response)

    async def __rpc_list_resources(
        self,
        stream: "grpclib.server.Stream[ListResourcesRequest, ListResourcesResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_resources(request)
        await stream.send_message(response)

    async def __rpc_search_resources(
        self,
        stream: "grpclib.server.Stream[SearchResourcesRequest, SearchResourcesResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.search_resources(request)
        await stream.send_message(response)

    async def __rpc_get_resource(
        self, stream: "grpclib.server.Stream[GetResourceRequest, Resource]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_resource(request)
        await stream.send_message(response)

    async def __rpc_get_resource_binary(
        self,
        stream: "grpclib.server.Stream[GetResourceBinaryRequest, ___google_api__.HttpBody]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_resource_binary(request)
        await stream.send_message(response)

    async def __rpc_update_resource(
        self, stream: "grpclib.server.Stream[UpdateResourceRequest, Resource]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_resource(request)
        await stream.send_message(response)

    async def __rpc_delete_resource(
        self,
        stream: "grpclib.server.Stream[DeleteResourceRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_resource(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.ResourceService/CreateResource": grpclib.const.Handler(
                self.__rpc_create_resource,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateResourceRequest,
                Resource,
            ),
            "/memos.api.v1.ResourceService/ListResources": grpclib.const.Handler(
                self.__rpc_list_resources,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListResourcesRequest,
                ListResourcesResponse,
            ),
            "/memos.api.v1.ResourceService/SearchResources": grpclib.const.Handler(
                self.__rpc_search_resources,
                grpclib.const.Cardinality.UNARY_UNARY,
                SearchResourcesRequest,
                SearchResourcesResponse,
            ),
            "/memos.api.v1.ResourceService/GetResource": grpclib.const.Handler(
                self.__rpc_get_resource,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetResourceRequest,
                Resource,
            ),
            "/memos.api.v1.ResourceService/GetResourceBinary": grpclib.const.Handler(
                self.__rpc_get_resource_binary,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetResourceBinaryRequest,
                ___google_api__.HttpBody,
            ),
            "/memos.api.v1.ResourceService/UpdateResource": grpclib.const.Handler(
                self.__rpc_update_resource,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateResourceRequest,
                Resource,
            ),
            "/memos.api.v1.ResourceService/DeleteResource": grpclib.const.Handler(
                self.__rpc_delete_resource,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteResourceRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class MemoServiceBase(ServiceBase):

    async def create_memo(self, create_memo_request: "CreateMemoRequest") -> "Memo":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memos(
        self, list_memos_request: "ListMemosRequest"
    ) -> "ListMemosResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def search_memos(
        self, search_memos_request: "SearchMemosRequest"
    ) -> "SearchMemosResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_memo(self, get_memo_request: "GetMemoRequest") -> "Memo":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_memo(self, update_memo_request: "UpdateMemoRequest") -> "Memo":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_memo(
        self, delete_memo_request: "DeleteMemoRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def export_memos(
        self, export_memos_request: "ExportMemosRequest"
    ) -> "ExportMemosResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memo_properties(
        self, list_memo_properties_request: "ListMemoPropertiesRequest"
    ) -> "ListMemoPropertiesResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def rebuild_memo_property(
        self, rebuild_memo_property_request: "RebuildMemoPropertyRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memo_tags(
        self, list_memo_tags_request: "ListMemoTagsRequest"
    ) -> "ListMemoTagsResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def rename_memo_tag(
        self, rename_memo_tag_request: "RenameMemoTagRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_memo_tag(
        self, delete_memo_tag_request: "DeleteMemoTagRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def set_memo_resources(
        self, set_memo_resources_request: "SetMemoResourcesRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memo_resources(
        self, list_memo_resources_request: "ListMemoResourcesRequest"
    ) -> "ListMemoResourcesResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def set_memo_relations(
        self, set_memo_relations_request: "SetMemoRelationsRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memo_relations(
        self, list_memo_relations_request: "ListMemoRelationsRequest"
    ) -> "ListMemoRelationsResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def create_memo_comment(
        self, create_memo_comment_request: "CreateMemoCommentRequest"
    ) -> "Memo":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memo_comments(
        self, list_memo_comments_request: "ListMemoCommentsRequest"
    ) -> "ListMemoCommentsResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_user_memos_stats(
        self, get_user_memos_stats_request: "GetUserMemosStatsRequest"
    ) -> "GetUserMemosStatsResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_memo_reactions(
        self, list_memo_reactions_request: "ListMemoReactionsRequest"
    ) -> "ListMemoReactionsResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def upsert_memo_reaction(
        self, upsert_memo_reaction_request: "UpsertMemoReactionRequest"
    ) -> "Reaction":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_memo_reaction(
        self, delete_memo_reaction_request: "DeleteMemoReactionRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_create_memo(
        self, stream: "grpclib.server.Stream[CreateMemoRequest, Memo]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_memo(request)
        await stream.send_message(response)

    async def __rpc_list_memos(
        self, stream: "grpclib.server.Stream[ListMemosRequest, ListMemosResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memos(request)
        await stream.send_message(response)

    async def __rpc_search_memos(
        self, stream: "grpclib.server.Stream[SearchMemosRequest, SearchMemosResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.search_memos(request)
        await stream.send_message(response)

    async def __rpc_get_memo(
        self, stream: "grpclib.server.Stream[GetMemoRequest, Memo]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_memo(request)
        await stream.send_message(response)

    async def __rpc_update_memo(
        self, stream: "grpclib.server.Stream[UpdateMemoRequest, Memo]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_memo(request)
        await stream.send_message(response)

    async def __rpc_delete_memo(
        self,
        stream: "grpclib.server.Stream[DeleteMemoRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_memo(request)
        await stream.send_message(response)

    async def __rpc_export_memos(
        self, stream: "grpclib.server.Stream[ExportMemosRequest, ExportMemosResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.export_memos(request)
        await stream.send_message(response)

    async def __rpc_list_memo_properties(
        self,
        stream: "grpclib.server.Stream[ListMemoPropertiesRequest, ListMemoPropertiesResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memo_properties(request)
        await stream.send_message(response)

    async def __rpc_rebuild_memo_property(
        self,
        stream: "grpclib.server.Stream[RebuildMemoPropertyRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.rebuild_memo_property(request)
        await stream.send_message(response)

    async def __rpc_list_memo_tags(
        self, stream: "grpclib.server.Stream[ListMemoTagsRequest, ListMemoTagsResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memo_tags(request)
        await stream.send_message(response)

    async def __rpc_rename_memo_tag(
        self,
        stream: "grpclib.server.Stream[RenameMemoTagRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.rename_memo_tag(request)
        await stream.send_message(response)

    async def __rpc_delete_memo_tag(
        self,
        stream: "grpclib.server.Stream[DeleteMemoTagRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_memo_tag(request)
        await stream.send_message(response)

    async def __rpc_set_memo_resources(
        self,
        stream: "grpclib.server.Stream[SetMemoResourcesRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.set_memo_resources(request)
        await stream.send_message(response)

    async def __rpc_list_memo_resources(
        self,
        stream: "grpclib.server.Stream[ListMemoResourcesRequest, ListMemoResourcesResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memo_resources(request)
        await stream.send_message(response)

    async def __rpc_set_memo_relations(
        self,
        stream: "grpclib.server.Stream[SetMemoRelationsRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.set_memo_relations(request)
        await stream.send_message(response)

    async def __rpc_list_memo_relations(
        self,
        stream: "grpclib.server.Stream[ListMemoRelationsRequest, ListMemoRelationsResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memo_relations(request)
        await stream.send_message(response)

    async def __rpc_create_memo_comment(
        self, stream: "grpclib.server.Stream[CreateMemoCommentRequest, Memo]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_memo_comment(request)
        await stream.send_message(response)

    async def __rpc_list_memo_comments(
        self,
        stream: "grpclib.server.Stream[ListMemoCommentsRequest, ListMemoCommentsResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memo_comments(request)
        await stream.send_message(response)

    async def __rpc_get_user_memos_stats(
        self,
        stream: "grpclib.server.Stream[GetUserMemosStatsRequest, GetUserMemosStatsResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_user_memos_stats(request)
        await stream.send_message(response)

    async def __rpc_list_memo_reactions(
        self,
        stream: "grpclib.server.Stream[ListMemoReactionsRequest, ListMemoReactionsResponse]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_memo_reactions(request)
        await stream.send_message(response)

    async def __rpc_upsert_memo_reaction(
        self, stream: "grpclib.server.Stream[UpsertMemoReactionRequest, Reaction]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.upsert_memo_reaction(request)
        await stream.send_message(response)

    async def __rpc_delete_memo_reaction(
        self,
        stream: "grpclib.server.Stream[DeleteMemoReactionRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_memo_reaction(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.MemoService/CreateMemo": grpclib.const.Handler(
                self.__rpc_create_memo,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateMemoRequest,
                Memo,
            ),
            "/memos.api.v1.MemoService/ListMemos": grpclib.const.Handler(
                self.__rpc_list_memos,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemosRequest,
                ListMemosResponse,
            ),
            "/memos.api.v1.MemoService/SearchMemos": grpclib.const.Handler(
                self.__rpc_search_memos,
                grpclib.const.Cardinality.UNARY_UNARY,
                SearchMemosRequest,
                SearchMemosResponse,
            ),
            "/memos.api.v1.MemoService/GetMemo": grpclib.const.Handler(
                self.__rpc_get_memo,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetMemoRequest,
                Memo,
            ),
            "/memos.api.v1.MemoService/UpdateMemo": grpclib.const.Handler(
                self.__rpc_update_memo,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateMemoRequest,
                Memo,
            ),
            "/memos.api.v1.MemoService/DeleteMemo": grpclib.const.Handler(
                self.__rpc_delete_memo,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteMemoRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.MemoService/ExportMemos": grpclib.const.Handler(
                self.__rpc_export_memos,
                grpclib.const.Cardinality.UNARY_UNARY,
                ExportMemosRequest,
                ExportMemosResponse,
            ),
            "/memos.api.v1.MemoService/ListMemoProperties": grpclib.const.Handler(
                self.__rpc_list_memo_properties,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemoPropertiesRequest,
                ListMemoPropertiesResponse,
            ),
            "/memos.api.v1.MemoService/RebuildMemoProperty": grpclib.const.Handler(
                self.__rpc_rebuild_memo_property,
                grpclib.const.Cardinality.UNARY_UNARY,
                RebuildMemoPropertyRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.MemoService/ListMemoTags": grpclib.const.Handler(
                self.__rpc_list_memo_tags,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemoTagsRequest,
                ListMemoTagsResponse,
            ),
            "/memos.api.v1.MemoService/RenameMemoTag": grpclib.const.Handler(
                self.__rpc_rename_memo_tag,
                grpclib.const.Cardinality.UNARY_UNARY,
                RenameMemoTagRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.MemoService/DeleteMemoTag": grpclib.const.Handler(
                self.__rpc_delete_memo_tag,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteMemoTagRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.MemoService/SetMemoResources": grpclib.const.Handler(
                self.__rpc_set_memo_resources,
                grpclib.const.Cardinality.UNARY_UNARY,
                SetMemoResourcesRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.MemoService/ListMemoResources": grpclib.const.Handler(
                self.__rpc_list_memo_resources,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemoResourcesRequest,
                ListMemoResourcesResponse,
            ),
            "/memos.api.v1.MemoService/SetMemoRelations": grpclib.const.Handler(
                self.__rpc_set_memo_relations,
                grpclib.const.Cardinality.UNARY_UNARY,
                SetMemoRelationsRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
            "/memos.api.v1.MemoService/ListMemoRelations": grpclib.const.Handler(
                self.__rpc_list_memo_relations,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemoRelationsRequest,
                ListMemoRelationsResponse,
            ),
            "/memos.api.v1.MemoService/CreateMemoComment": grpclib.const.Handler(
                self.__rpc_create_memo_comment,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateMemoCommentRequest,
                Memo,
            ),
            "/memos.api.v1.MemoService/ListMemoComments": grpclib.const.Handler(
                self.__rpc_list_memo_comments,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemoCommentsRequest,
                ListMemoCommentsResponse,
            ),
            "/memos.api.v1.MemoService/GetUserMemosStats": grpclib.const.Handler(
                self.__rpc_get_user_memos_stats,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetUserMemosStatsRequest,
                GetUserMemosStatsResponse,
            ),
            "/memos.api.v1.MemoService/ListMemoReactions": grpclib.const.Handler(
                self.__rpc_list_memo_reactions,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListMemoReactionsRequest,
                ListMemoReactionsResponse,
            ),
            "/memos.api.v1.MemoService/UpsertMemoReaction": grpclib.const.Handler(
                self.__rpc_upsert_memo_reaction,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpsertMemoReactionRequest,
                Reaction,
            ),
            "/memos.api.v1.MemoService/DeleteMemoReaction": grpclib.const.Handler(
                self.__rpc_delete_memo_reaction,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteMemoReactionRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class WebhookServiceBase(ServiceBase):

    async def create_webhook(
        self, create_webhook_request: "CreateWebhookRequest"
    ) -> "Webhook":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def get_webhook(self, get_webhook_request: "GetWebhookRequest") -> "Webhook":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def list_webhooks(
        self, list_webhooks_request: "ListWebhooksRequest"
    ) -> "ListWebhooksResponse":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def update_webhook(
        self, update_webhook_request: "UpdateWebhookRequest"
    ) -> "Webhook":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def delete_webhook(
        self, delete_webhook_request: "DeleteWebhookRequest"
    ) -> "betterproto_lib_google_protobuf.Empty":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_create_webhook(
        self, stream: "grpclib.server.Stream[CreateWebhookRequest, Webhook]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.create_webhook(request)
        await stream.send_message(response)

    async def __rpc_get_webhook(
        self, stream: "grpclib.server.Stream[GetWebhookRequest, Webhook]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_webhook(request)
        await stream.send_message(response)

    async def __rpc_list_webhooks(
        self, stream: "grpclib.server.Stream[ListWebhooksRequest, ListWebhooksResponse]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.list_webhooks(request)
        await stream.send_message(response)

    async def __rpc_update_webhook(
        self, stream: "grpclib.server.Stream[UpdateWebhookRequest, Webhook]"
    ) -> None:
        request = await stream.recv_message()
        response = await self.update_webhook(request)
        await stream.send_message(response)

    async def __rpc_delete_webhook(
        self,
        stream: "grpclib.server.Stream[DeleteWebhookRequest, betterproto_lib_google_protobuf.Empty]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.delete_webhook(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.WebhookService/CreateWebhook": grpclib.const.Handler(
                self.__rpc_create_webhook,
                grpclib.const.Cardinality.UNARY_UNARY,
                CreateWebhookRequest,
                Webhook,
            ),
            "/memos.api.v1.WebhookService/GetWebhook": grpclib.const.Handler(
                self.__rpc_get_webhook,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetWebhookRequest,
                Webhook,
            ),
            "/memos.api.v1.WebhookService/ListWebhooks": grpclib.const.Handler(
                self.__rpc_list_webhooks,
                grpclib.const.Cardinality.UNARY_UNARY,
                ListWebhooksRequest,
                ListWebhooksResponse,
            ),
            "/memos.api.v1.WebhookService/UpdateWebhook": grpclib.const.Handler(
                self.__rpc_update_webhook,
                grpclib.const.Cardinality.UNARY_UNARY,
                UpdateWebhookRequest,
                Webhook,
            ),
            "/memos.api.v1.WebhookService/DeleteWebhook": grpclib.const.Handler(
                self.__rpc_delete_webhook,
                grpclib.const.Cardinality.UNARY_UNARY,
                DeleteWebhookRequest,
                betterproto_lib_google_protobuf.Empty,
            ),
        }


class WorkspaceServiceBase(ServiceBase):

    async def get_workspace_profile(
        self, get_workspace_profile_request: "GetWorkspaceProfileRequest"
    ) -> "WorkspaceProfile":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_get_workspace_profile(
        self,
        stream: "grpclib.server.Stream[GetWorkspaceProfileRequest, WorkspaceProfile]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_workspace_profile(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.WorkspaceService/GetWorkspaceProfile": grpclib.const.Handler(
                self.__rpc_get_workspace_profile,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetWorkspaceProfileRequest,
                WorkspaceProfile,
            ),
        }


class WorkspaceSettingServiceBase(ServiceBase):

    async def get_workspace_setting(
        self, get_workspace_setting_request: "GetWorkspaceSettingRequest"
    ) -> "WorkspaceSetting":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def set_workspace_setting(
        self, set_workspace_setting_request: "SetWorkspaceSettingRequest"
    ) -> "WorkspaceSetting":
        raise grpclib.GRPCError(grpclib.const.Status.UNIMPLEMENTED)

    async def __rpc_get_workspace_setting(
        self,
        stream: "grpclib.server.Stream[GetWorkspaceSettingRequest, WorkspaceSetting]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.get_workspace_setting(request)
        await stream.send_message(response)

    async def __rpc_set_workspace_setting(
        self,
        stream: "grpclib.server.Stream[SetWorkspaceSettingRequest, WorkspaceSetting]",
    ) -> None:
        request = await stream.recv_message()
        response = await self.set_workspace_setting(request)
        await stream.send_message(response)

    def __mapping__(self) -> Dict[str, grpclib.const.Handler]:
        return {
            "/memos.api.v1.WorkspaceSettingService/GetWorkspaceSetting": grpclib.const.Handler(
                self.__rpc_get_workspace_setting,
                grpclib.const.Cardinality.UNARY_UNARY,
                GetWorkspaceSettingRequest,
                WorkspaceSetting,
            ),
            "/memos.api.v1.WorkspaceSettingService/SetWorkspaceSetting": grpclib.const.Handler(
                self.__rpc_set_workspace_setting,
                grpclib.const.Cardinality.UNARY_UNARY,
                SetWorkspaceSettingRequest,
                WorkspaceSetting,
            ),
        }
