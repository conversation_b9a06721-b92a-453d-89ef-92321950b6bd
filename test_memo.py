#!/usr/bin/env python3
import asyncio
from mcp_server_memos import Config
from mcp_server_memos.server import MemoServiceToolAdapter

async def create_memo():
    # Configure for your Memos server
    config = Config(
        host="*************",
        port=5230,
        token="eyJhbGciOiJIUzI1NiIsImtpZCI6InYxIiwidHlwIjoiSldUIn0.eyJuYW1lIjoicml6d2FuYXNoYWZpNzQ0IiwiaXNzIjoibWVtb3MiLCJzdWIiOiIzIiwiYXVkIjpbInVzZXIuYWNjZXNzLXRva2VuIl0sImlhdCI6MTc0ODA5Nzc0MX0.rTt22iPkklSdnXUVOHKKYA9vHyG5juIuIIGviSpMugY"
    )
    
    # Create the tool adapter
    adapter = MemoServiceToolAdapter(config)
    
    # Create the memo
    try:
        result = await adapter.create_memo({
            "content": "machine learning",
            "visibility": "PRIVATE"
        })
        print("✅ Memo created successfully!")
        for content in result:
            print(f"📝 {content.text}")
    except Exception as e:
        print(f"❌ Error creating memo: {e}")

if __name__ == "__main__":
    asyncio.run(create_memo())
